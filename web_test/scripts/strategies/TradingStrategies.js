// Trading Strategies
// Collection of trading strategies for backtesting

/**
 * RSI Mean Reversion Strategy
 * Buy when RSI < 30, Sell when RSI > 70
 */
export class RSIMeanReversionStrategy {
    constructor(config = {}) {
        this.name = 'RSI Mean Reversion';
        this.config = {
            rsiOversold: 30,
            rsiOverbought: 70,
            stopLossPercent: 0.05, // 5%
            takeProfitPercent: 0.10, // 10%
            ...config
        };
    }
    
    getSignal(data) {
        const { indicators, price, portfolio } = data;
        
        if (!indicators.rsi || !indicators.rsi.value) return null;
        
        const rsi = indicators.rsi.value;
        const currentPrice = price.close;
        const hasPosition = portfolio.positions.stock > 0;
        
        // Buy signal: RSI oversold and no current position
        if (rsi < this.config.rsiOversold && !hasPosition) {
            return {
                type: 'buy',
                quantity: Math.floor(portfolio.cash * 0.95 / currentPrice), // Use 95% of cash
                stopLoss: currentPrice * (1 - this.config.stopLossPercent),
                takeProfit: currentPrice * (1 + this.config.takeProfitPercent),
                reason: `RSI oversold: ${rsi.toFixed(2)}`
            };
        }
        
        // Sell signal: RSI overbought and has position
        if (rsi > this.config.rsiOverbought && hasPosition) {
            return {
                type: 'sell',
                quantity: portfolio.positions.stock,
                reason: `RSI overbought: ${rsi.toFixed(2)}`
            };
        }
        
        return null;
    }
}

/**
 * MACD Crossover Strategy
 * Buy when MACD crosses above signal line, Sell when crosses below
 */
export class MACDCrossoverStrategy {
    constructor(config = {}) {
        this.name = 'MACD Crossover';
        this.config = {
            stopLossPercent: 0.03, // 3%
            takeProfitPercent: 0.08, // 8%
            ...config
        };
        this.previousMACD = null;
        this.previousSignal = null;
    }
    
    getSignal(data) {
        const { indicators, price, portfolio } = data;
        
        if (!indicators.macd || !indicators.macd.macd || !indicators.macd.signal) return null;
        
        const macd = indicators.macd.macd;
        const signal = indicators.macd.signal;
        const currentPrice = price.close;
        const hasPosition = portfolio.positions.stock > 0;
        
        // Check for crossover
        if (this.previousMACD !== null && this.previousSignal !== null) {
            const bullishCrossover = this.previousMACD <= this.previousSignal && macd > signal;
            const bearishCrossover = this.previousMACD >= this.previousSignal && macd < signal;
            
            // Buy signal: Bullish crossover and no position
            if (bullishCrossover && !hasPosition) {
                this.previousMACD = macd;
                this.previousSignal = signal;
                
                return {
                    type: 'buy',
                    quantity: Math.floor(portfolio.cash * 0.9 / currentPrice),
                    stopLoss: currentPrice * (1 - this.config.stopLossPercent),
                    takeProfit: currentPrice * (1 + this.config.takeProfitPercent),
                    reason: `MACD bullish crossover: ${macd.toFixed(4)} > ${signal.toFixed(4)}`
                };
            }
            
            // Sell signal: Bearish crossover and has position
            if (bearishCrossover && hasPosition) {
                this.previousMACD = macd;
                this.previousSignal = signal;
                
                return {
                    type: 'sell',
                    quantity: portfolio.positions.stock,
                    reason: `MACD bearish crossover: ${macd.toFixed(4)} < ${signal.toFixed(4)}`
                };
            }
        }
        
        this.previousMACD = macd;
        this.previousSignal = signal;
        return null;
    }
}

/**
 * Moving Average Crossover Strategy
 * Buy when short MA crosses above long MA, Sell when crosses below
 */
export class MovingAverageCrossoverStrategy {
    constructor(config = {}) {
        this.name = 'MA Crossover';
        this.config = {
            shortPeriod: 20,
            longPeriod: 50,
            stopLossPercent: 0.04, // 4%
            takeProfitPercent: 0.12, // 12%
            ...config
        };
        this.previousShortMA = null;
        this.previousLongMA = null;
    }
    
    getSignal(data) {
        const { indicators, price, portfolio } = data;
        
        if (!indicators.ma) return null;
        
        // Get moving averages (assuming we have SMA20 and SMA50)
        const shortMA = indicators.ma.sma20 || indicators.ma[`sma${this.config.shortPeriod}`];
        const longMA = indicators.ma.sma50 || indicators.ma[`sma${this.config.longPeriod}`];
        
        if (!shortMA || !longMA) return null;
        
        const currentPrice = price.close;
        const hasPosition = portfolio.positions.stock > 0;
        
        // Check for crossover
        if (this.previousShortMA !== null && this.previousLongMA !== null) {
            const bullishCrossover = this.previousShortMA <= this.previousLongMA && shortMA > longMA;
            const bearishCrossover = this.previousShortMA >= this.previousLongMA && shortMA < longMA;
            
            // Buy signal: Golden cross and no position
            if (bullishCrossover && !hasPosition) {
                this.previousShortMA = shortMA;
                this.previousLongMA = longMA;
                
                return {
                    type: 'buy',
                    quantity: Math.floor(portfolio.cash * 0.85 / currentPrice),
                    stopLoss: currentPrice * (1 - this.config.stopLossPercent),
                    takeProfit: currentPrice * (1 + this.config.takeProfitPercent),
                    reason: `Golden cross: MA${this.config.shortPeriod}(${shortMA.toFixed(2)}) > MA${this.config.longPeriod}(${longMA.toFixed(2)})`
                };
            }
            
            // Sell signal: Death cross and has position
            if (bearishCrossover && hasPosition) {
                this.previousShortMA = shortMA;
                this.previousLongMA = longMA;
                
                return {
                    type: 'sell',
                    quantity: portfolio.positions.stock,
                    reason: `Death cross: MA${this.config.shortPeriod}(${shortMA.toFixed(2)}) < MA${this.config.longPeriod}(${longMA.toFixed(2)})`
                };
            }
        }
        
        this.previousShortMA = shortMA;
        this.previousLongMA = longMA;
        return null;
    }
}

/**
 * Bollinger Bands Strategy
 * Buy when price touches lower band, Sell when price touches upper band
 */
export class BollingerBandsStrategy {
    constructor(config = {}) {
        this.name = 'Bollinger Bands';
        this.config = {
            stopLossPercent: 0.03, // 3%
            takeProfitPercent: 0.06, // 6%
            ...config
        };
    }
    
    getSignal(data) {
        const { indicators, price, portfolio } = data;
        
        if (!indicators.bb || !indicators.bb.upper || !indicators.bb.lower) return null;
        
        const upperBand = indicators.bb.upper;
        const lowerBand = indicators.bb.lower;
        const currentPrice = price.close;
        const hasPosition = portfolio.positions.stock > 0;
        
        // Buy signal: Price touches or goes below lower band
        if (currentPrice <= lowerBand && !hasPosition) {
            return {
                type: 'buy',
                quantity: Math.floor(portfolio.cash * 0.8 / currentPrice),
                stopLoss: currentPrice * (1 - this.config.stopLossPercent),
                takeProfit: currentPrice * (1 + this.config.takeProfitPercent),
                reason: `Price at lower BB: ${currentPrice} <= ${lowerBand.toFixed(2)}`
            };
        }
        
        // Sell signal: Price touches or goes above upper band
        if (currentPrice >= upperBand && hasPosition) {
            return {
                type: 'sell',
                quantity: portfolio.positions.stock,
                reason: `Price at upper BB: ${currentPrice} >= ${upperBand.toFixed(2)}`
            };
        }
        
        return null;
    }
}

/**
 * Multi-Indicator Strategy
 * Combines RSI, MACD, and Moving Averages for stronger signals
 */
export class MultiIndicatorStrategy {
    constructor(config = {}) {
        this.name = 'Multi-Indicator';
        this.config = {
            rsiOversold: 35,
            rsiOverbought: 65,
            stopLossPercent: 0.04, // 4%
            takeProfitPercent: 0.08, // 8%
            ...config
        };
        this.previousMACD = null;
        this.previousSignal = null;
        this.previousShortMA = null;
        this.previousLongMA = null;
    }
    
    getSignal(data) {
        const { indicators, price, portfolio } = data;
        
        if (!indicators.rsi || !indicators.macd || !indicators.ma) return null;
        
        const rsi = indicators.rsi.value;
        const macd = indicators.macd.macd;
        const signal = indicators.macd.signal;
        const shortMA = indicators.ma.sma20;
        const longMA = indicators.ma.sma50;
        const currentPrice = price.close;
        const hasPosition = portfolio.positions.stock > 0;
        
        // Calculate signal strength
        let bullishSignals = 0;
        let bearishSignals = 0;
        const reasons = [];
        
        // RSI signals
        if (rsi < this.config.rsiOversold) {
            bullishSignals++;
            reasons.push(`RSI oversold: ${rsi.toFixed(2)}`);
        } else if (rsi > this.config.rsiOverbought) {
            bearishSignals++;
            reasons.push(`RSI overbought: ${rsi.toFixed(2)}`);
        }
        
        // MACD signals
        if (this.previousMACD !== null && this.previousSignal !== null) {
            if (this.previousMACD <= this.previousSignal && macd > signal) {
                bullishSignals++;
                reasons.push('MACD bullish crossover');
            } else if (this.previousMACD >= this.previousSignal && macd < signal) {
                bearishSignals++;
                reasons.push('MACD bearish crossover');
            }
        }
        
        // Moving Average signals
        if (this.previousShortMA !== null && this.previousLongMA !== null) {
            if (this.previousShortMA <= this.previousLongMA && shortMA > longMA) {
                bullishSignals++;
                reasons.push('MA golden cross');
            } else if (this.previousShortMA >= this.previousLongMA && shortMA < longMA) {
                bearishSignals++;
                reasons.push('MA death cross');
            }
        }
        
        // Update previous values
        this.previousMACD = macd;
        this.previousSignal = signal;
        this.previousShortMA = shortMA;
        this.previousLongMA = longMA;
        
        // Generate signals only with multiple confirmations
        if (bullishSignals >= 2 && !hasPosition) {
            return {
                type: 'buy',
                quantity: Math.floor(portfolio.cash * 0.7 / currentPrice),
                stopLoss: currentPrice * (1 - this.config.stopLossPercent),
                takeProfit: currentPrice * (1 + this.config.takeProfitPercent),
                reason: `Multi-indicator buy: ${reasons.join(', ')}`
            };
        }
        
        if (bearishSignals >= 2 && hasPosition) {
            return {
                type: 'sell',
                quantity: portfolio.positions.stock,
                reason: `Multi-indicator sell: ${reasons.join(', ')}`
            };
        }
        
        return null;
    }
}

/**
 * Buy and Hold Strategy
 * Simple buy and hold for comparison
 */
export class BuyAndHoldStrategy {
    constructor(config = {}) {
        this.name = 'Buy and Hold';
        this.config = config;
        this.hasBought = false;
    }
    
    getSignal(data) {
        const { price, portfolio } = data;
        
        if (!this.hasBought && portfolio.cash > 0) {
            this.hasBought = true;
            return {
                type: 'buy',
                quantity: Math.floor(portfolio.cash * 0.99 / price.close),
                reason: 'Buy and hold initial purchase'
            };
        }
        
        return null;
    }
}

// Export all strategies
export const strategies = {
    RSIMeanReversionStrategy,
    MACDCrossoverStrategy,
    MovingAverageCrossoverStrategy,
    BollingerBandsStrategy,
    MultiIndicatorStrategy,
    BuyAndHoldStrategy
};
