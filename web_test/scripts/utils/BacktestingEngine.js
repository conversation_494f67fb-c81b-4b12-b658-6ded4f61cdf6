// Backtesting Engine
// Tests trading strategies against historical data

import { DataTransformer } from './DataTransformer.js';

export class BacktestingEngine {
    constructor() {
        this.strategies = new Map();
        this.results = new Map();
        this.defaultConfig = {
            initialCapital: 100000000, // 100M VND
            commission: 0.0015, // 0.15%
            slippage: 0.001, // 0.1%
            maxPositionSize: 0.1, // 10% of portfolio
            riskFreeRate: 0.05 // 5% annual
        };
    }
    
    /**
     * Register a trading strategy
     */
    registerStrategy(name, strategy) {
        this.strategies.set(name, strategy);
    }
    
    /**
     * Run backtest for a strategy
     */
    async runBacktest(strategyName, symbol, startDate, endDate, config = {}) {
        const strategy = this.strategies.get(strategyName);
        if (!strategy) {
            throw new Error(`Strategy ${strategyName} not found`);
        }
        
        const testConfig = { ...this.defaultConfig, ...config };
        
        // Load historical data
        const priceData = await this.loadHistoricalData(symbol, startDate, endDate);
        const indicatorData = await this.loadIndicatorData(symbol, startDate, endDate);
        
        // Initialize portfolio
        const portfolio = new Portfolio(testConfig.initialCapital);
        const trades = [];
        const equity = [];
        
        // Run strategy simulation
        for (let i = 0; i < priceData.length; i++) {
            const currentData = {
                price: priceData[i],
                indicators: this.getIndicatorsAtIndex(indicatorData, i),
                portfolio: portfolio.getSnapshot(),
                index: i
            };
            
            // Get strategy signal
            const signal = strategy.getSignal(currentData);
            
            // Execute trades based on signal
            if (signal) {
                const trade = this.executeTrade(signal, currentData, portfolio, testConfig);
                if (trade) {
                    trades.push(trade);
                }
            }
            
            // Update portfolio value
            portfolio.updateValue(priceData[i]);
            equity.push({
                date: priceData[i].time,
                value: portfolio.getTotalValue(),
                cash: portfolio.cash,
                positions: portfolio.getPositions()
            });
        }
        
        // Calculate performance metrics
        const results = this.calculatePerformanceMetrics(equity, trades, testConfig);
        
        // Store results
        this.results.set(`${strategyName}_${symbol}`, {
            strategy: strategyName,
            symbol,
            startDate,
            endDate,
            config: testConfig,
            trades,
            equity,
            performance: results
        });
        
        return results;
    }
    
    /**
     * Load historical price data
     */
    async loadHistoricalData(symbol, startDate, endDate) {
        try {
            const response = await fetch(`analysis/${symbol}_prices.json`);
            const data = await response.json();
            const transformedData = DataTransformer.transformPriceHistory(data);
            
            // Filter by date range
            return transformedData.filter(point => {
                const date = new Date(point.time * 1000);
                return date >= startDate && date <= endDate;
            });
        } catch (error) {
            console.error('Failed to load historical data:', error);
            return [];
        }
    }
    
    /**
     * Load indicator data
     */
    async loadIndicatorData(symbol, startDate, endDate) {
        const indicators = ['rsi', 'macd', 'ma', 'bb'];
        const indicatorData = {};
        
        for (const indicator of indicators) {
            try {
                const response = await fetch(`analysis/${symbol}_${indicator}.json`);
                const data = await response.json();
                const transformedData = DataTransformer.transformTechnicalIndicator(data, indicator);
                
                // Filter by date range
                indicatorData[indicator] = transformedData.filter(point => {
                    const date = new Date(point.time * 1000);
                    return date >= startDate && date <= endDate;
                });
            } catch (error) {
                console.warn(`Failed to load ${indicator} data:`, error);
                indicatorData[indicator] = [];
            }
        }
        
        return indicatorData;
    }
    
    /**
     * Get indicators at specific index
     */
    getIndicatorsAtIndex(indicatorData, index) {
        const indicators = {};
        
        Object.keys(indicatorData).forEach(indicator => {
            if (indicatorData[indicator][index]) {
                indicators[indicator] = indicatorData[indicator][index];
            }
        });
        
        return indicators;
    }
    
    /**
     * Execute a trade based on signal
     */
    executeTrade(signal, data, portfolio, config) {
        const { type, quantity, stopLoss, takeProfit } = signal;
        const price = data.price.close;
        const timestamp = data.price.time;
        
        // Calculate position size
        const maxValue = portfolio.getTotalValue() * config.maxPositionSize;
        const actualQuantity = Math.min(quantity, Math.floor(maxValue / price));
        
        if (actualQuantity <= 0) return null;
        
        // Calculate costs
        const tradeValue = actualQuantity * price;
        const commission = tradeValue * config.commission;
        const slippage = tradeValue * config.slippage;
        const totalCost = tradeValue + commission + slippage;
        
        // Check if portfolio has enough cash
        if (type === 'buy' && portfolio.cash < totalCost) {
            return null;
        }
        
        // Execute trade
        const trade = {
            id: `${timestamp}_${type}`,
            timestamp,
            type,
            symbol: data.price.symbol || 'UNKNOWN',
            quantity: actualQuantity,
            price,
            commission,
            slippage,
            stopLoss,
            takeProfit,
            status: 'open'
        };
        
        if (type === 'buy') {
            portfolio.buy(actualQuantity, price, commission + slippage);
        } else if (type === 'sell') {
            portfolio.sell(actualQuantity, price, commission + slippage);
        }
        
        return trade;
    }
    
    /**
     * Calculate performance metrics
     */
    calculatePerformanceMetrics(equity, trades, config) {
        if (equity.length === 0) return {};
        
        const initialValue = equity[0].value;
        const finalValue = equity[equity.length - 1].value;
        const totalReturn = (finalValue - initialValue) / initialValue;
        
        // Calculate daily returns
        const dailyReturns = [];
        for (let i = 1; i < equity.length; i++) {
            const dailyReturn = (equity[i].value - equity[i - 1].value) / equity[i - 1].value;
            dailyReturns.push(dailyReturn);
        }
        
        // Calculate metrics
        const avgDailyReturn = dailyReturns.reduce((sum, ret) => sum + ret, 0) / dailyReturns.length;
        const volatility = this.calculateVolatility(dailyReturns);
        const sharpeRatio = this.calculateSharpeRatio(avgDailyReturn, volatility, config.riskFreeRate);
        const maxDrawdown = this.calculateMaxDrawdown(equity);
        
        // Trade statistics
        const completedTrades = trades.filter(trade => trade.status === 'closed');
        const winningTrades = completedTrades.filter(trade => trade.pnl > 0);
        const losingTrades = completedTrades.filter(trade => trade.pnl < 0);
        
        return {
            totalReturn: totalReturn * 100,
            annualizedReturn: this.annualizeReturn(totalReturn, equity.length),
            volatility: volatility * 100,
            sharpeRatio,
            maxDrawdown: maxDrawdown * 100,
            totalTrades: trades.length,
            winningTrades: winningTrades.length,
            losingTrades: losingTrades.length,
            winRate: completedTrades.length > 0 ? (winningTrades.length / completedTrades.length * 100) : 0,
            avgWin: winningTrades.length > 0 ? winningTrades.reduce((sum, trade) => sum + trade.pnl, 0) / winningTrades.length : 0,
            avgLoss: losingTrades.length > 0 ? losingTrades.reduce((sum, trade) => sum + trade.pnl, 0) / losingTrades.length : 0,
            profitFactor: this.calculateProfitFactor(winningTrades, losingTrades),
            finalValue,
            initialValue
        };
    }
    
    /**
     * Calculate volatility (standard deviation of returns)
     */
    calculateVolatility(returns) {
        const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
        const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length;
        return Math.sqrt(variance);
    }
    
    /**
     * Calculate Sharpe ratio
     */
    calculateSharpeRatio(avgReturn, volatility, riskFreeRate) {
        const dailyRiskFreeRate = riskFreeRate / 252; // Assuming 252 trading days
        return volatility > 0 ? (avgReturn - dailyRiskFreeRate) / volatility : 0;
    }
    
    /**
     * Calculate maximum drawdown
     */
    calculateMaxDrawdown(equity) {
        let maxDrawdown = 0;
        let peak = equity[0].value;
        
        for (const point of equity) {
            if (point.value > peak) {
                peak = point.value;
            }
            
            const drawdown = (peak - point.value) / peak;
            if (drawdown > maxDrawdown) {
                maxDrawdown = drawdown;
            }
        }
        
        return maxDrawdown;
    }
    
    /**
     * Annualize return
     */
    annualizeReturn(totalReturn, periods) {
        const years = periods / 252; // Assuming 252 trading days per year
        return (Math.pow(1 + totalReturn, 1 / years) - 1) * 100;
    }
    
    /**
     * Calculate profit factor
     */
    calculateProfitFactor(winningTrades, losingTrades) {
        const totalWins = winningTrades.reduce((sum, trade) => sum + trade.pnl, 0);
        const totalLosses = Math.abs(losingTrades.reduce((sum, trade) => sum + trade.pnl, 0));
        
        return totalLosses > 0 ? totalWins / totalLosses : totalWins > 0 ? Infinity : 0;
    }
    
    /**
     * Get backtest results
     */
    getResults(strategyName, symbol) {
        return this.results.get(`${strategyName}_${symbol}`);
    }
    
    /**
     * Get all results
     */
    getAllResults() {
        return Array.from(this.results.values());
    }
}

/**
 * Portfolio class for tracking positions and cash
 */
class Portfolio {
    constructor(initialCash) {
        this.cash = initialCash;
        this.positions = new Map();
        this.initialValue = initialCash;
    }
    
    buy(quantity, price, costs) {
        const totalCost = quantity * price + costs;
        if (this.cash >= totalCost) {
            this.cash -= totalCost;
            const currentPosition = this.positions.get('stock') || 0;
            this.positions.set('stock', currentPosition + quantity);
            return true;
        }
        return false;
    }
    
    sell(quantity, price, costs) {
        const currentPosition = this.positions.get('stock') || 0;
        if (currentPosition >= quantity) {
            const proceeds = quantity * price - costs;
            this.cash += proceeds;
            this.positions.set('stock', currentPosition - quantity);
            return true;
        }
        return false;
    }
    
    updateValue(priceData) {
        // Portfolio value is updated based on current price
        this.currentPrice = priceData.close;
    }
    
    getTotalValue() {
        const stockValue = (this.positions.get('stock') || 0) * (this.currentPrice || 0);
        return this.cash + stockValue;
    }
    
    getSnapshot() {
        return {
            cash: this.cash,
            positions: new Map(this.positions),
            totalValue: this.getTotalValue()
        };
    }
    
    getPositions() {
        return Object.fromEntries(this.positions);
    }
}

// Export singleton instance
export const backtestingEngine = new BacktestingEngine();
