// Performance Monitoring Utility
// Tracks application performance metrics and provides optimization insights

export class PerformanceMonitor {
    constructor() {
        this.metrics = {
            dataLoading: {},
            chartRendering: {},
            componentUpdates: {},
            memoryUsage: [],
            networkRequests: {}
        };

        this.observers = {
            performance: null,
            memory: null,
            network: null
        };

        this.isMonitoring = false;
        this.startTime = Date.now();
    }

    /**
     * Start performance monitoring
     */
    start() {
        if (this.isMonitoring) return;

        this.isMonitoring = true;
        this.startTime = Date.now();

        // Monitor performance entries
        this.observePerformanceEntries();

        // Monitor memory usage
        this.observeMemoryUsage();

        // Monitor network requests
        this.observeNetworkRequests();

        console.log('📊 Performance monitoring started');
    }

    /**
     * Stop performance monitoring
     */
    stop() {
        if (!this.isMonitoring) return;

        this.isMonitoring = false;

        // Disconnect observers
        Object.values(this.observers).forEach(observer => {
            if (observer && observer.disconnect) {
                observer.disconnect();
            }
        });

        console.log('📊 Performance monitoring stopped');
        return this.generateReport();
    }

    /**
     * Track data loading performance
     */
    trackDataLoading(symbol, type, startTime, endTime, success = true, error = null) {
        const duration = endTime - startTime;
        const key = `${symbol}_${type}`;

        if (!this.metrics.dataLoading[key]) {
            this.metrics.dataLoading[key] = [];
        }

        this.metrics.dataLoading[key].push({
            timestamp: Date.now(),
            duration,
            success,
            error: error?.message || null,
            size: 0 // Will be updated if available
        });

        if (duration > 2000) {
            console.warn(`⚠️ Slow data loading: ${key} took ${duration}ms`);
        }
    }

    /**
     * Track chart rendering performance
     */
    trackChartRendering(symbol, chartType, startTime, endTime, dataPoints = 0) {
        const duration = endTime - startTime;
        const key = `${symbol}_${chartType}`;

        if (!this.metrics.chartRendering[key]) {
            this.metrics.chartRendering[key] = [];
        }

        this.metrics.chartRendering[key].push({
            timestamp: Date.now(),
            duration,
            dataPoints,
            fps: dataPoints > 0 ? Math.round(1000 / duration * dataPoints) : 0
        });

        if (duration > 1000) {
            console.warn(`⚠️ Slow chart rendering: ${key} took ${duration}ms for ${dataPoints} points`);
        }
    }

    /**
     * Track component update performance
     */
    trackComponentUpdate(componentName, startTime, endTime, reason = 'unknown') {
        const duration = endTime - startTime;

        if (!this.metrics.componentUpdates[componentName]) {
            this.metrics.componentUpdates[componentName] = [];
        }

        this.metrics.componentUpdates[componentName].push({
            timestamp: Date.now(),
            duration,
            reason
        });

        if (duration > 100) {
            console.warn(`⚠️ Slow component update: ${componentName} took ${duration}ms (${reason})`);
        }
    }

    /**
     * Observe performance entries
     */
    observePerformanceEntries() {
        if (!window.PerformanceObserver) return;

        try {
            this.observers.performance = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.entryType === 'measure') {
                        this.processMeasureEntry(entry);
                    } else if (entry.entryType === 'navigation') {
                        this.processNavigationEntry(entry);
                    }
                });
            });

            this.observers.performance.observe({ entryTypes: ['measure', 'navigation'] });
        } catch (error) {
            console.warn('Performance observer not supported:', error);
        }
    }

    /**
     * Observe memory usage
     */
    observeMemoryUsage() {
        if (!performance.memory) return;

        const recordMemory = () => {
            if (!this.isMonitoring) return;

            const memory = {
                timestamp: Date.now(),
                used: performance.memory.usedJSHeapSize,
                total: performance.memory.totalJSHeapSize,
                limit: performance.memory.jsHeapSizeLimit
            };

            this.metrics.memoryUsage.push(memory);

            // Keep only last 100 entries
            if (this.metrics.memoryUsage.length > 100) {
                this.metrics.memoryUsage.shift();
            }

            // Check for memory leaks
            if (memory.used > memory.limit * 0.8) {
                console.warn('⚠️ High memory usage detected:', memory);
            }

            setTimeout(recordMemory, 5000); // Record every 5 seconds
        };

        recordMemory();
    }

    /**
     * Observe network requests
     */
    observeNetworkRequests() {
        if (!window.PerformanceObserver) return;

        try {
            this.observers.network = new PerformanceObserver((list) => {
                const entries = list.getEntries();
                entries.forEach(entry => {
                    if (entry.name.includes('analysis/')) {
                        this.processNetworkEntry(entry);
                    }
                });
            });

            this.observers.network.observe({ entryTypes: ['resource'] });
        } catch (error) {
            console.warn('Network observer not supported:', error);
        }
    }

    /**
     * Process measure performance entry
     */
    processMeasureEntry(entry) {
        const { name, duration } = entry;

        if (name.includes('chart-render')) {
            const [, symbol, chartType] = name.split('-');
            this.trackChartRendering(symbol, chartType, 0, duration);
        } else if (name.includes('component-update')) {
            const [, componentName] = name.split('-');
            this.trackComponentUpdate(componentName, 0, duration);
        }
    }

    /**
     * Process navigation performance entry
     */
    processNavigationEntry(entry) {
        this.metrics.navigation = {
            domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
            loadComplete: entry.loadEventEnd - entry.loadEventStart,
            totalTime: entry.loadEventEnd - entry.navigationStart
        };
    }

    /**
     * Process network performance entry
     */
    processNetworkEntry(entry) {
        const url = new URL(entry.name);
        const filename = url.pathname.split('/').pop();

        if (!this.metrics.networkRequests[filename]) {
            this.metrics.networkRequests[filename] = [];
        }

        this.metrics.networkRequests[filename].push({
            timestamp: Date.now(),
            duration: entry.duration,
            size: entry.transferSize || 0,
            cached: entry.transferSize === 0 && entry.decodedBodySize > 0
        });
    }

    /**
     * Generate performance report
     */
    generateReport() {
        const report = {
            summary: this.generateSummary(),
            dataLoading: this.analyzeDataLoading(),
            chartRendering: this.analyzeChartRendering(),
            componentUpdates: this.analyzeComponentUpdates(),
            memoryUsage: this.analyzeMemoryUsage(),
            networkRequests: this.analyzeNetworkRequests(),
            recommendations: this.generateRecommendations()
        };

        console.log('📊 Performance Report:', report);
        return report;
    }

    /**
     * Generate performance summary
     */
    generateSummary() {
        const totalTime = Date.now() - this.startTime;
        const dataLoadingCount = Object.values(this.metrics.dataLoading).flat().length;
        const chartRenderingCount = Object.values(this.metrics.chartRendering).flat().length;
        const componentUpdateCount = Object.values(this.metrics.componentUpdates).flat().length;

        return {
            monitoringDuration: totalTime,
            totalDataLoads: dataLoadingCount,
            totalChartRenders: chartRenderingCount,
            totalComponentUpdates: componentUpdateCount,
            memorySnapshots: this.metrics.memoryUsage.length
        };
    }

    /**
     * Analyze data loading performance
     */
    analyzeDataLoading() {
        const allLoads = Object.values(this.metrics.dataLoading).flat();
        if (allLoads.length === 0) return { average: 0, slowest: null, failures: 0 };

        const durations = allLoads.map(load => load.duration);
        const failures = allLoads.filter(load => !load.success);

        return {
            average: durations.reduce((a, b) => a + b, 0) / durations.length,
            slowest: Math.max(...durations),
            fastest: Math.min(...durations),
            failures: failures.length,
            successRate: ((allLoads.length - failures.length) / allLoads.length * 100).toFixed(1)
        };
    }

    /**
     * Analyze chart rendering performance
     */
    analyzeChartRendering() {
        const allRenders = Object.values(this.metrics.chartRendering).flat();
        if (allRenders.length === 0) return { average: 0, slowest: null };

        const durations = allRenders.map(render => render.duration);

        return {
            average: durations.reduce((a, b) => a + b, 0) / durations.length,
            slowest: Math.max(...durations),
            fastest: Math.min(...durations),
            averageFPS: allRenders.reduce((sum, render) => sum + render.fps, 0) / allRenders.length
        };
    }

    /**
     * Analyze component updates
     */
    analyzeComponentUpdates() {
        const allUpdates = Object.values(this.metrics.componentUpdates).flat();
        if (allUpdates.length === 0) return { average: 0, slowest: null };

        const durations = allUpdates.map(update => update.duration);

        return {
            average: durations.reduce((a, b) => a + b, 0) / durations.length,
            slowest: Math.max(...durations),
            fastest: Math.min(...durations),
            updateReasons: this.groupUpdateReasons(allUpdates)
        };
    }

    /**
     * Analyze memory usage
     */
    analyzeMemoryUsage() {
        if (this.metrics.memoryUsage.length === 0) return { trend: 'unknown' };

        const latest = this.metrics.memoryUsage[this.metrics.memoryUsage.length - 1];
        const earliest = this.metrics.memoryUsage[0];

        return {
            current: latest.used,
            peak: Math.max(...this.metrics.memoryUsage.map(m => m.used)),
            trend: latest.used > earliest.used ? 'increasing' : 'stable',
            utilizationPercent: (latest.used / latest.limit * 100).toFixed(1)
        };
    }

    /**
     * Analyze network requests
     */
    analyzeNetworkRequests() {
        const allRequests = Object.values(this.metrics.networkRequests).flat();
        if (allRequests.length === 0) return { average: 0, cached: 0 };

        const durations = allRequests.map(req => req.duration);
        const cached = allRequests.filter(req => req.cached);

        return {
            total: allRequests.length,
            average: durations.reduce((a, b) => a + b, 0) / durations.length,
            cached: cached.length,
            cacheHitRate: (cached.length / allRequests.length * 100).toFixed(1)
        };
    }

    /**
     * Group component update reasons
     */
    groupUpdateReasons(updates) {
        const reasons = {};
        updates.forEach(update => {
            reasons[update.reason] = (reasons[update.reason] || 0) + 1;
        });
        return reasons;
    }

    /**
     * Generate performance recommendations
     */
    generateRecommendations() {
        const recommendations = [];

        // Data loading recommendations
        const dataAnalysis = this.analyzeDataLoading();
        if (dataAnalysis.average > 1000) {
            recommendations.push('Consider implementing data pagination or lazy loading for better performance');
        }
        if (parseFloat(dataAnalysis.successRate) < 95) {
            recommendations.push('Improve error handling and retry logic for data loading');
        }

        // Chart rendering recommendations
        const chartAnalysis = this.analyzeChartRendering();
        if (chartAnalysis.average > 500) {
            recommendations.push('Optimize chart rendering by reducing data points or using data sampling');
        }

        // Memory recommendations
        const memoryAnalysis = this.analyzeMemoryUsage();
        if (memoryAnalysis.trend === 'increasing') {
            recommendations.push('Monitor for memory leaks and implement proper cleanup in components');
        }

        // Network recommendations
        const networkAnalysis = this.analyzeNetworkRequests();
        if (parseFloat(networkAnalysis.cacheHitRate) < 50) {
            recommendations.push('Implement better caching strategies for frequently accessed data');
        }

        return recommendations;
    }
}

// Export singleton instance
export const performanceMonitor = new PerformanceMonitor();
