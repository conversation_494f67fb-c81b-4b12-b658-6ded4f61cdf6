// BacktestingView Component
// Interface for running and viewing backtesting results

import { backtestingEngine } from '../utils/BacktestingEngine.js';
import { strategies } from '../strategies/TradingStrategies.js';

export const BacktestingView = {
    oninit: (vnode) => {
        vnode.state.selectedStrategy = 'RSIMeanReversionStrategy';
        vnode.state.selectedSymbol = 'HPG';
        vnode.state.startDate = new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // 1 year ago
        vnode.state.endDate = new Date().toISOString().split('T')[0]; // Today
        vnode.state.isRunning = false;
        vnode.state.results = null;
        vnode.state.error = null;
        
        // Register strategies
        Object.entries(strategies).forEach(([name, StrategyClass]) => {
            const strategy = new StrategyClass();
            backtestingEngine.registerStrategy(name, strategy);
        });
    },
    
    view: (vnode) => {
        const { state } = vnode.state;
        
        return m('div.backtesting-view.p-6', [
            // Header
            m('div.mb-6', [
                m('h1.text-2xl.font-bold.mb-2', 'Strategy Backtesting'),
                m('p.text-muted-foreground', 'Test trading strategies against historical data')
            ]),
            
            // Configuration Panel
            m('div.bg-card.border.border-border.rounded-lg.p-6.mb-6', [
                m('h2.text-lg.font-semibold.mb-4', 'Backtest Configuration'),
                
                m('div.grid.grid-cols-1.md\\:grid-cols-2.lg\\:grid-cols-4.gap-4', [
                    // Strategy Selection
                    m('div', [
                        m('label.block.text-sm.font-medium.mb-2', 'Strategy'),
                        m('select.w-full.px-3.py-2.border.border-border.rounded-md.bg-background', {
                            value: vnode.state.selectedStrategy,
                            onchange: (e) => vnode.state.selectedStrategy = e.target.value
                        }, [
                            Object.keys(strategies).map(strategyName =>
                                m('option', { value: strategyName }, 
                                    strategyName.replace(/Strategy$/, '').replace(/([A-Z])/g, ' $1').trim()
                                )
                            )
                        ])
                    ]),
                    
                    // Symbol Selection
                    m('div', [
                        m('label.block.text-sm.font-medium.mb-2', 'Symbol'),
                        m('select.w-full.px-3.py-2.border.border-border.rounded-md.bg-background', {
                            value: vnode.state.selectedSymbol,
                            onchange: (e) => vnode.state.selectedSymbol = e.target.value
                        }, [
                            ['HPG', 'TCB', 'VCB', 'VIC', 'VHM', 'MSN', 'VRE', 'PLX', 'GAS', 'CTG'].map(symbol =>
                                m('option', { value: symbol }, symbol)
                            )
                        ])
                    ]),
                    
                    // Start Date
                    m('div', [
                        m('label.block.text-sm.font-medium.mb-2', 'Start Date'),
                        m('input[type="date"].w-full.px-3.py-2.border.border-border.rounded-md.bg-background', {
                            value: vnode.state.startDate,
                            onchange: (e) => vnode.state.startDate = e.target.value
                        })
                    ]),
                    
                    // End Date
                    m('div', [
                        m('label.block.text-sm.font-medium.mb-2', 'End Date'),
                        m('input[type="date"].w-full.px-3.py-2.border.border-border.rounded-md.bg-background', {
                            value: vnode.state.endDate,
                            onchange: (e) => vnode.state.endDate = e.target.value
                        })
                    ])
                ]),
                
                // Run Button
                m('div.mt-6', [
                    m('button.bg-primary.text-primary-foreground.px-6.py-2.rounded-md.hover\\:bg-primary\\/90.transition-colors.disabled\\:opacity-50', {
                        disabled: vnode.state.isRunning,
                        onclick: () => runBacktest(vnode)
                    }, [
                        vnode.state.isRunning && m('span.material-symbols-outlined.animate-spin.mr-2', 'refresh'),
                        vnode.state.isRunning ? 'Running Backtest...' : 'Run Backtest'
                    ])
                ])
            ]),
            
            // Error Display
            vnode.state.error && m('div.bg-red-50.border.border-red-200.rounded-lg.p-4.mb-6', [
                m('div.flex.items-center.gap-2.text-red-800', [
                    m('span.material-symbols-outlined', 'error'),
                    m('span.font-medium', 'Backtest Error')
                ]),
                m('p.text-red-700.mt-2', vnode.state.error)
            ]),
            
            // Results Display
            vnode.state.results && m(BacktestResults, { results: vnode.state.results })
        ]);
    }
};

// Backtest Results Component
const BacktestResults = {
    view: (vnode) => {
        const { results } = vnode.attrs;
        const { performance } = results;
        
        return m('div.bg-card.border.border-border.rounded-lg.p-6', [
            m('h2.text-lg.font-semibold.mb-4', 'Backtest Results'),
            
            // Performance Metrics Grid
            m('div.grid.grid-cols-2.md\\:grid-cols-4.gap-4.mb-6', [
                m(MetricCard, {
                    title: 'Total Return',
                    value: `${performance.totalReturn.toFixed(2)}%`,
                    color: performance.totalReturn >= 0 ? 'green' : 'red'
                }),
                m(MetricCard, {
                    title: 'Annualized Return',
                    value: `${performance.annualizedReturn.toFixed(2)}%`,
                    color: performance.annualizedReturn >= 0 ? 'green' : 'red'
                }),
                m(MetricCard, {
                    title: 'Sharpe Ratio',
                    value: performance.sharpeRatio.toFixed(2),
                    color: performance.sharpeRatio >= 1 ? 'green' : performance.sharpeRatio >= 0 ? 'yellow' : 'red'
                }),
                m(MetricCard, {
                    title: 'Max Drawdown',
                    value: `${performance.maxDrawdown.toFixed(2)}%`,
                    color: 'red'
                }),
                m(MetricCard, {
                    title: 'Win Rate',
                    value: `${performance.winRate.toFixed(1)}%`,
                    color: performance.winRate >= 50 ? 'green' : 'red'
                }),
                m(MetricCard, {
                    title: 'Total Trades',
                    value: performance.totalTrades.toString(),
                    color: 'blue'
                }),
                m(MetricCard, {
                    title: 'Profit Factor',
                    value: performance.profitFactor.toFixed(2),
                    color: performance.profitFactor >= 1 ? 'green' : 'red'
                }),
                m(MetricCard, {
                    title: 'Volatility',
                    value: `${performance.volatility.toFixed(2)}%`,
                    color: 'gray'
                })
            ]),
            
            // Detailed Statistics
            m('div.grid.grid-cols-1.lg\\:grid-cols-2.gap-6', [
                // Trade Statistics
                m('div', [
                    m('h3.text-md.font-semibold.mb-3', 'Trade Statistics'),
                    m('div.space-y-2', [
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Winning Trades:'),
                            m('span.text-green-600.font-medium', performance.winningTrades)
                        ]),
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Losing Trades:'),
                            m('span.text-red-600.font-medium', performance.losingTrades)
                        ]),
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Average Win:'),
                            m('span.text-green-600.font-medium', `${(performance.avgWin || 0).toLocaleString()} VND`)
                        ]),
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Average Loss:'),
                            m('span.text-red-600.font-medium', `${(performance.avgLoss || 0).toLocaleString()} VND`)
                        ])
                    ])
                ]),
                
                // Portfolio Value
                m('div', [
                    m('h3.text-md.font-semibold.mb-3', 'Portfolio Value'),
                    m('div.space-y-2', [
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Initial Value:'),
                            m('span.font-medium', `${performance.initialValue.toLocaleString()} VND`)
                        ]),
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Final Value:'),
                            m('span.font-medium', `${performance.finalValue.toLocaleString()} VND`)
                        ]),
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Absolute Gain:'),
                            m(`span.font-medium.${performance.finalValue >= performance.initialValue ? 'text-green-600' : 'text-red-600'}`, 
                                `${(performance.finalValue - performance.initialValue).toLocaleString()} VND`)
                        ])
                    ])
                ])
            ]),
            
            // Equity Curve Placeholder
            m('div.mt-6', [
                m('h3.text-md.font-semibold.mb-3', 'Equity Curve'),
                m('div.h-64.bg-muted\\/30.rounded-lg.border-2.border-dashed.border-border.flex.items-center.justify-center', [
                    m('div.text-center.text-muted-foreground', [
                        m('span.material-symbols-outlined.text-4xl.mb-2', 'show_chart'),
                        m('p', 'Equity curve chart will be implemented here')
                    ])
                ])
            ])
        ]);
    }
};

// Metric Card Component
const MetricCard = {
    view: (vnode) => {
        const { title, value, color } = vnode.attrs;
        
        const colorClasses = {
            green: 'text-green-600',
            red: 'text-red-600',
            yellow: 'text-yellow-600',
            blue: 'text-blue-600',
            gray: 'text-gray-600'
        };
        
        return m('div.bg-muted\\/30.rounded-lg.p-4', [
            m('div.text-sm.text-muted-foreground.mb-1', title),
            m(`div.text-xl.font-bold.${colorClasses[color] || 'text-foreground'}`, value)
        ]);
    }
};

// Run backtest function
async function runBacktest(vnode) {
    vnode.state.isRunning = true;
    vnode.state.error = null;
    vnode.state.results = null;
    m.redraw();
    
    try {
        const startDate = new Date(vnode.state.startDate);
        const endDate = new Date(vnode.state.endDate);
        
        if (startDate >= endDate) {
            throw new Error('Start date must be before end date');
        }
        
        const results = await backtestingEngine.runBacktest(
            vnode.state.selectedStrategy,
            vnode.state.selectedSymbol,
            startDate,
            endDate
        );
        
        vnode.state.results = backtestingEngine.getResults(vnode.state.selectedStrategy, vnode.state.selectedSymbol);
        
    } catch (error) {
        vnode.state.error = error.message;
        console.error('Backtest error:', error);
    } finally {
        vnode.state.isRunning = false;
        m.redraw();
    }
}
