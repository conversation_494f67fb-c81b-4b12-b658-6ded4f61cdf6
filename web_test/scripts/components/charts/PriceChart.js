// PriceChart Component
// Displays stock price chart using Lightweight Charts

import { DataTransformer } from '../../utils/DataTransformer.js';

export const PriceChart = {
    oninit: (vnode) => {
        vnode.state.loading = true;
        vnode.state.error = null;
        vnode.state.chart = null;
        vnode.state.lastSymbol = null;
    },

    oncreate: (vnode) => {
        const { symbol, state } = vnode.attrs;
        initializeChart(vnode.dom, symbol, state, vnode.state);
    },

    onupdate: (vnode) => {
        const { symbol, state } = vnode.attrs;
        if (vnode.state.lastSymbol !== symbol) {
            vnode.state.lastSymbol = symbol;
            vnode.state.loading = true;
            vnode.state.error = null;
            updateChart(vnode.dom, symbol, state, vnode.state);
        }
    },

    onremove: (vnode) => {
        if (vnode.state.chart) {
            vnode.state.chart.remove();
        }
        if (vnode.state.resizeObserver) {
            vnode.state.resizeObserver.disconnect();
        }
    },

    view: (vnode) => {
        const { symbol, state } = vnode.attrs;
        const { loading, error } = vnode.state;

        return m('div.chart-container', [
            m('div.chart-header.mb-4', [
                m('h3.text-lg.font-semibold.flex.items-center.gap-2', [
                    `${symbol} - Biểu đồ giá`,
                    loading && m('span.material-symbols-outlined.animate-spin.text-primary', 'refresh')
                ]),
                m('div.flex.gap-2.mt-2', [
                    m('button.px-3.py-1.text-xs.bg-primary.text-primary-foreground.rounded', {
                        onclick: () => state.setChartTimeframe('1D')
                    }, '1D'),
                    m('button.px-3.py-1.text-xs.bg-muted.text-muted-foreground.rounded.hover\\:bg-muted\\/80', {
                        onclick: () => state.setChartTimeframe('1W')
                    }, '1W'),
                    m('button.px-3.py-1.text-xs.bg-muted.text-muted-foreground.rounded.hover\\:bg-muted\\/80', {
                        onclick: () => state.setChartTimeframe('1M')
                    }, '1M'),
                    m('button.px-3.py-1.text-xs.bg-muted.text-muted-foreground.rounded.hover\\:bg-muted\\/80', {
                        onclick: () => state.setChartTimeframe('3M')
                    }, '3M'),
                    m('button.px-3.py-1.text-xs.bg-muted.text-muted-foreground.rounded.hover\\:bg-muted\\/80', {
                        onclick: () => state.setChartTimeframe('1Y')
                    }, '1Y')
                ])
            ]),
            error ?
                m('div.flex.items-center.justify-center.h-96.bg-muted\\/30.rounded-lg.border-2.border-dashed.border-border', [
                    m('div.text-center', [
                        m('span.material-symbols-outlined.text-4xl.text-muted-foreground.mb-2', 'error'),
                        m('p.text-muted-foreground', `Không thể tải dữ liệu biểu đồ cho ${symbol}`),
                        m('p.text-xs.text-muted-foreground.mt-1', error)
                    ])
                ]) :
                m('div#chart-container', {
                    style: 'height: 400px; width: 100%;'
                })
        ]);
    }
};

function initializeChart(container, symbol, appState, componentState) {
    if (!window.LightweightCharts) {
        componentState.error = 'Lightweight Charts library not loaded';
        componentState.loading = false;
        console.error('Lightweight Charts library not loaded');
        return;
    }

    const chartContainer = container.querySelector('#chart-container');
    if (!chartContainer) {
        componentState.error = 'Chart container not found';
        componentState.loading = false;
        return;
    }

    try {
        // Create chart
        const chart = LightweightCharts.createChart(chartContainer, {
            width: chartContainer.clientWidth,
            height: 400,
            layout: {
                background: { color: 'transparent' },
                textColor: '#333',
            },
            grid: {
                vertLines: { color: '#e1e5e9' },
                horzLines: { color: '#e1e5e9' },
            },
            crosshair: {
                mode: LightweightCharts.CrosshairMode.Normal,
            },
            rightPriceScale: {
                borderColor: '#cccccc',
            },
            timeScale: {
                borderColor: '#cccccc',
                timeVisible: true,
                secondsVisible: false,
            },
        });

        // Create candlestick series
        const candlestickSeries = chart.addCandlestickSeries({
            upColor: '#26a69a',
            downColor: '#ef5350',
            borderVisible: false,
            wickUpColor: '#26a69a',
            wickDownColor: '#ef5350',
        });

        // Store chart reference
        componentState.chart = chart;
        componentState.candlestickSeries = candlestickSeries;

        // Load and display data
        updateChart(container, symbol, appState, componentState);

        // Handle resize
        const resizeObserver = new ResizeObserver(entries => {
            if (entries.length === 0 || entries[0].target !== chartContainer) return;
            const { width, height } = entries[0].contentRect;
            chart.applyOptions({ width, height });
        });

        resizeObserver.observe(chartContainer);
        componentState.resizeObserver = resizeObserver;

    } catch (error) {
        componentState.error = `Failed to initialize chart: ${error.message}`;
        componentState.loading = false;
        console.error('Chart initialization error:', error);
    }
}

function updateChart(container, symbol, appState, componentState) {
    if (!componentState.chart || !componentState.candlestickSeries) return;

    // Get price history from cache
    const priceHistory = appState.priceHistoryCache[symbol];
    if (!priceHistory) {
        // Try to load price data
        loadPriceData(symbol, appState, componentState).then(() => {
            updateChart(container, symbol, appState, componentState);
        });
        return;
    }

    try {
        // Transform price data for chart
        const transformedData = DataTransformer.transformPriceHistory(priceHistory);
        if (!transformedData || transformedData.length === 0) {
            componentState.error = `No price data available for ${symbol}`;
            componentState.loading = false;
            console.warn('No price data available for', symbol);
            return;
        }

        // Convert to chart format
        const chartData = transformedData.map(point => ({
            time: point.time,
            open: point.open,
            high: point.high,
            low: point.low,
            close: point.close
        }));

        // Update chart data
        componentState.candlestickSeries.setData(chartData);

        // Fit content
        componentState.chart.timeScale().fitContent();

        // Mark as loaded
        componentState.loading = false;
        componentState.error = null;

    } catch (error) {
        componentState.error = `Failed to update chart: ${error.message}`;
        componentState.loading = false;
        console.error('Chart update error:', error);
    }
}

async function loadPriceData(symbol, appState, componentState) {
    if (!symbol || symbol === 'Watchlist') return;

    try {
        const response = await fetch(`analysis/${symbol}_prices.json`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const priceHistory = await response.json();
        appState.priceHistoryCache[symbol] = priceHistory;
        m.redraw();
    } catch (error) {
        componentState.error = `Failed to load price data: ${error.message}`;
        componentState.loading = false;
        console.warn('Failed to load price data for', symbol, error);
    }
}

// Volume Chart Component
export const VolumeChart = {
    oninit: (vnode) => {
        vnode.state.loading = true;
        vnode.state.error = null;
        vnode.state.chart = null;
        vnode.state.lastSymbol = null;
    },

    oncreate: (vnode) => {
        const { symbol, state } = vnode.attrs;
        initializeVolumeChart(vnode.dom, symbol, state, vnode.state);
    },

    onupdate: (vnode) => {
        const { symbol, state } = vnode.attrs;
        if (vnode.state.lastSymbol !== symbol) {
            vnode.state.lastSymbol = symbol;
            vnode.state.loading = true;
            vnode.state.error = null;
            updateVolumeChart(vnode.dom, symbol, state, vnode.state);
        }
    },

    onremove: (vnode) => {
        if (vnode.state.chart) {
            vnode.state.chart.remove();
        }
    },

    view: (vnode) => {
        const { symbol } = vnode.attrs;
        const { loading, error } = vnode.state;

        return m('div.volume-chart-container', [
            m('div.chart-header.mb-2', [
                m('h4.text-md.font-medium.flex.items-center.gap-2', [
                    `${symbol} - Khối lượng giao dịch`,
                    loading && m('span.material-symbols-outlined.animate-spin.text-primary.text-sm', 'refresh')
                ])
            ]),
            error ?
                m('div.flex.items-center.justify-center.h-36.bg-muted\\/30.rounded-lg.border-2.border-dashed.border-border', [
                    m('div.text-center', [
                        m('span.material-symbols-outlined.text-2xl.text-muted-foreground.mb-1', 'error'),
                        m('p.text-xs.text-muted-foreground', 'Không thể tải dữ liệu khối lượng')
                    ])
                ]) :
                m('div#volume-chart-container', {
                    style: 'height: 150px; width: 100%;'
                })
        ]);
    }
};

function initializeVolumeChart(container, symbol, appState, componentState) {
    if (!window.LightweightCharts) {
        componentState.error = 'Lightweight Charts library not loaded';
        componentState.loading = false;
        console.error('Lightweight Charts library not loaded');
        return;
    }

    const chartContainer = container.querySelector('#volume-chart-container');
    if (!chartContainer) {
        componentState.error = 'Chart container not found';
        componentState.loading = false;
        return;
    }

    try {
        // Create volume chart
        const chart = LightweightCharts.createChart(chartContainer, {
            width: chartContainer.clientWidth,
            height: 150,
            layout: {
                background: { color: 'transparent' },
                textColor: '#333',
            },
            grid: {
                vertLines: { color: '#e1e5e9' },
                horzLines: { color: '#e1e5e9' },
            },
            rightPriceScale: {
                borderColor: '#cccccc',
            },
            timeScale: {
                borderColor: '#cccccc',
                timeVisible: true,
                secondsVisible: false,
            },
        });

        // Create histogram series for volume
        const volumeSeries = chart.addHistogramSeries({
            color: '#26a69a',
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: '',
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });

        // Store chart reference
        componentState.chart = chart;
        componentState.volumeSeries = volumeSeries;

        // Load and display data
        updateVolumeChart(container, symbol, appState, componentState);

    } catch (error) {
        componentState.error = `Failed to initialize volume chart: ${error.message}`;
        componentState.loading = false;
        console.error('Volume chart initialization error:', error);
    }
}

function updateVolumeChart(container, symbol, appState, componentState) {
    if (!componentState.chart || !componentState.volumeSeries) return;

    // Get price history from cache
    const priceHistory = appState.priceHistoryCache[symbol];
    if (!priceHistory) {
        // Try to load price data
        loadPriceData(symbol, appState, componentState).then(() => {
            updateVolumeChart(container, symbol, appState, componentState);
        });
        return;
    }

    try {
        // Transform price data for volume chart
        const transformedData = DataTransformer.transformPriceHistory(priceHistory);
        if (!transformedData || transformedData.length === 0) {
            componentState.error = `No volume data available for ${symbol}`;
            componentState.loading = false;
            return;
        }

        // Convert to volume chart format
        const volumeData = transformedData.map(point => ({
            time: point.time,
            value: point.volume || 0,
            color: point.close >= point.open ? '#26a69a' : '#ef5350'
        }));

        // Update chart data
        componentState.volumeSeries.setData(volumeData);

        // Fit content
        componentState.chart.timeScale().fitContent();

        // Mark as loaded
        componentState.loading = false;
        componentState.error = null;

    } catch (error) {
        componentState.error = `Failed to update volume chart: ${error.message}`;
        componentState.loading = false;
        console.error('Volume chart update error:', error);
    }
}

// Simple Line Chart for indicators
export const IndicatorChart = {
    oninit: (vnode) => {
        vnode.state.loading = true;
        vnode.state.error = null;
        vnode.state.chart = null;
        vnode.state.lastKey = null;
    },

    oncreate: (vnode) => {
        const { symbol, indicator, state } = vnode.attrs;
        initializeIndicatorChart(vnode.dom, symbol, indicator, state, vnode.state);
    },

    onupdate: (vnode) => {
        const { symbol, indicator, state } = vnode.attrs;
        const key = `${symbol}_${indicator}`;
        if (vnode.state.lastKey !== key) {
            vnode.state.lastKey = key;
            vnode.state.loading = true;
            vnode.state.error = null;
            updateIndicatorChart(vnode.dom, symbol, indicator, state, vnode.state);
        }
    },

    onremove: (vnode) => {
        if (vnode.state.chart) {
            vnode.state.chart.remove();
        }
    },

    view: (vnode) => {
        const { symbol, indicator } = vnode.attrs;
        const { loading, error } = vnode.state;

        return m('div.indicator-chart-container', [
            m('div.chart-header.mb-2', [
                m('h4.text-md.font-medium.flex.items-center.gap-2', [
                    `${symbol} - ${indicator.toUpperCase()}`,
                    loading && m('span.material-symbols-outlined.animate-spin.text-primary.text-sm', 'refresh')
                ])
            ]),
            error ?
                m('div.flex.items-center.justify-center.h-48.bg-muted\\/30.rounded-lg.border-2.border-dashed.border-border', [
                    m('div.text-center', [
                        m('span.material-symbols-outlined.text-2xl.text-muted-foreground.mb-1', 'error'),
                        m('p.text-xs.text-muted-foreground', `Không thể tải ${indicator.toUpperCase()}`)
                    ])
                ]) :
                m('div#indicator-chart-container', {
                    style: 'height: 200px; width: 100%;'
                })
        ]);
    }
};

function initializeIndicatorChart(container, symbol, indicator, appState, componentState) {
    if (!window.LightweightCharts) {
        componentState.error = 'Lightweight Charts library not loaded';
        componentState.loading = false;
        console.error('Lightweight Charts library not loaded');
        return;
    }

    const chartContainer = container.querySelector('#indicator-chart-container');
    if (!chartContainer) {
        componentState.error = 'Chart container not found';
        componentState.loading = false;
        return;
    }

    try {
        // Create indicator chart
        const chart = LightweightCharts.createChart(chartContainer, {
            width: chartContainer.clientWidth,
            height: 200,
            layout: {
                background: { color: 'transparent' },
                textColor: '#333',
            },
            grid: {
                vertLines: { color: '#e1e5e9' },
                horzLines: { color: '#e1e5e9' },
            },
            rightPriceScale: {
                borderColor: '#cccccc',
            },
            timeScale: {
                borderColor: '#cccccc',
                timeVisible: true,
                secondsVisible: false,
            },
        });

        // Create line series for indicator
        const lineSeries = chart.addLineSeries({
            color: '#2196F3',
            lineWidth: 2,
        });

        // Store chart reference
        componentState.chart = chart;
        componentState.lineSeries = lineSeries;

        // Load and display data
        updateIndicatorChart(container, symbol, indicator, appState, componentState);

    } catch (error) {
        componentState.error = `Failed to initialize ${indicator} chart: ${error.message}`;
        componentState.loading = false;
        console.error('Indicator chart initialization error:', error);
    }
}

function updateIndicatorChart(container, symbol, indicator, appState, componentState) {
    if (!componentState.chart || !componentState.lineSeries) return;

    // Try to load indicator data
    loadIndicatorData(symbol, indicator, appState, componentState).then(() => {
        const indicatorData = appState.indicatorDataCache[`${symbol}_${indicator}`];
        if (!indicatorData) {
            componentState.error = `No ${indicator} data available for ${symbol}`;
            componentState.loading = false;
            return;
        }

        try {
            // Transform indicator data
            const transformedData = DataTransformer.transformTechnicalIndicator(indicatorData, indicator);
            if (!transformedData || transformedData.length === 0) {
                componentState.error = `No ${indicator} data points available`;
                componentState.loading = false;
                return;
            }

            // Convert to chart format (simple line for now)
            const chartData = transformedData.map(point => ({
                time: point.time,
                value: point.value || point.rsi || point.macd || 0
            })).filter(point => point.value !== null && point.value !== undefined);

            if (chartData.length === 0) {
                componentState.error = `No valid ${indicator} data points`;
                componentState.loading = false;
                return;
            }

            // Update chart data
            componentState.lineSeries.setData(chartData);

            // Fit content
            componentState.chart.timeScale().fitContent();

            // Mark as loaded
            componentState.loading = false;
            componentState.error = null;

        } catch (error) {
            componentState.error = `Failed to update ${indicator} chart: ${error.message}`;
            componentState.loading = false;
            console.error('Indicator chart update error:', error);
        }
    }).catch(error => {
        componentState.error = `Failed to load ${indicator} data: ${error.message}`;
        componentState.loading = false;
    });
}

async function loadIndicatorData(symbol, indicator, appState, componentState) {
    if (!symbol || symbol === 'Watchlist') return;

    const cacheKey = `${symbol}_${indicator}`;
    if (appState.indicatorDataCache[cacheKey]) return;

    try {
        const response = await fetch(`analysis/${symbol}_${indicator}.json`);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        const indicatorData = await response.json();
        appState.indicatorDataCache[cacheKey] = indicatorData;
        m.redraw();
    } catch (error) {
        componentState.error = `Failed to load ${indicator} data: ${error.message}`;
        componentState.loading = false;
        console.warn('Failed to load indicator data for', symbol, indicator, error);
        throw error; // Re-throw to be caught by the calling function
    }
}
