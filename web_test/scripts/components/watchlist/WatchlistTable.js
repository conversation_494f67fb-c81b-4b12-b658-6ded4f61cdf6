// WatchlistTable Component
// Displays the stock table with all watchlist data

import { DataTransformer } from '../../utils/DataTransformer.js';

export const WatchlistTable = {
    view: (vnode) => {
        const { state } = vnode.attrs;

        // Get all symbols from selected watchlists
        let allSymbols = new Set();

        if (state.selectedWatchlists.length === 0) {
            return m('div#watchlist-table-container', [
                m('div.rounded-md.bg-muted.p-4.text-sm.text-muted-foreground', [
                    m('p', '<PERSON><PERSON><PERSON> chọn ít nhất một danh sách theo dõi để xem cổ phiếu, hoặc tạo một danh sách mới.')
                ])
            ]);
        }

        // Collect all symbols from selected watchlists
        state.selectedWatchlists.forEach(watchlistId => {
            if (state.watchlists[watchlistId]) {
                state.watchlists[watchlistId].forEach(symbol => allSymbols.add(symbol));
            }
        });

        if (allSymbols.size === 0) {
            return m('div#watchlist-table-container', [
                m('div.rounded-md.bg-muted.p-4.text-sm.text-muted-foreground', [
                    m('p', 'Chưa có cổ phiếu nào trong danh sách theo dõi. Thêm cổ phiếu bằng cách sử dụng hộp tìm kiếm bên trên.')
                ])
            ]);
        }

        // Get stock data for the symbols
        const stocksToDisplay = [];
        allSymbols.forEach(symbol => {
            // Try to get real data first
            const realData = state.stockDataCache[symbol];
            if (realData) {
                const transformedData = DataTransformer.transformStockAnalysis(realData);
                stocksToDisplay.push({
                    symbol: transformedData.symbol,
                    name: transformedData.symbol,
                    price: DataTransformer.formatPrice(transformedData.price.current),
                    change: DataTransformer.formatPrice(transformedData.price.change),
                    changePercent: DataTransformer.formatPercentage(transformedData.price.changePercent).text,
                    trend: transformedData.trend.direction === 'tăng' ? 'up' :
                           transformedData.trend.direction === 'giảm' ? 'down' : 'neutral',
                    realData: transformedData
                });
            } else {
                // Load real data asynchronously
                state.loadStockData(symbol).then(() => m.redraw());

                // Fallback to sample data or placeholder
                const sampleData = state.sampleStocks.find(stock => stock.symbol === symbol);
                if (sampleData) {
                    stocksToDisplay.push(sampleData);
                } else {
                    stocksToDisplay.push({
                        symbol: symbol,
                        name: symbol,
                        price: 'Loading...',
                        change: 'N/A',
                        changePercent: 'N/A',
                        trend: 'neutral'
                    });
                }
            }
        });

        return m('div#watchlist-table-container', [
            m('div.rounded-lg.border.border-border.bg-card.shadow-sm', [
                m('div.relative.w-full.overflow-auto', [
                    m('table.w-full.caption-bottom.text-sm', [
                        // Table header
                        m('thead.\\[\\&_tr\\]\\:border-b.bg-muted\\/30', [
                            m('tr.border-b.border-border.transition-colors', [
                                m('th.h-14.px-4.text-center.align-middle.font-semibold.text-foreground', [
                                    m('span.material-symbols-outlined.text-lg', 'image')
                                ]),
                                m('th.h-14.px-4.text-left.align-middle.font-semibold.text-foreground', [
                                    m('div.flex.items-center.gap-2', [
                                        m('span.material-symbols-outlined.text-lg', 'trending_up'),
                                        'Mã CK'
                                    ])
                                ]),
                                m('th.h-14.px-4.text-right.align-middle.font-semibold.text-foreground', [
                                    m('div.flex.items-center.justify-end.gap-2', [
                                        m('span.material-symbols-outlined.text-lg', 'attach_money'),
                                        'Giá hiện tại'
                                    ])
                                ]),
                                m('th.h-14.px-4.text-left.align-middle.font-semibold.text-foreground', [
                                    m('div.flex.items-center.gap-2', [
                                        m('span.material-symbols-outlined.text-lg', 'trending_up'),
                                        'Xu hướng & Tin cậy'
                                    ])
                                ]),
                                m('th.h-14.px-4.text-center.align-middle.font-semibold.text-foreground', [
                                    m('div.flex.items-center.justify-center.gap-2', [
                                        m('span.material-symbols-outlined.text-lg', 'analytics'),
                                        'Phân tích kỹ thuật'
                                    ])
                                ]),
                                m('th.h-14.px-4.text-left.align-middle.font-semibold.text-foreground', {
                                    style: 'min-width: 280px; max-width: 320px;'
                                }, [
                                    m('div.flex.items-center.gap-2', [
                                        m('span.material-symbols-outlined.text-lg', 'lightbulb'),
                                        'Khuyến nghị & Chiến lược'
                                    ])
                                ])
                            ])
                        ]),

                        // Table body
                        m('tbody.\\[\\&_tr\\:last-child\\]\\:border-0',
                            stocksToDisplay.map(stock => m(StockRow, { stock, state }))
                        )
                    ])
                ])
            ])
        ]);
    }
};

const StockRow = {
    view: (vnode) => {
        const { stock, state } = vnode.attrs;

        let trendClass = '';
        let trendIcon = 'trending_flat';
        if (stock.trend === 'up') {
            trendClass = 'text-green-600';
            trendIcon = 'trending_up';
        } else if (stock.trend === 'down') {
            trendClass = 'text-red-600';
            trendIcon = 'trending_down';
        }

        // Use real data if available, otherwise generate mock data
        let trendDirection, strength, confidence, recommendation;

        if (stock.realData) {
            trendDirection = stock.realData.trend.direction;
            strength = stock.realData.trend.strength;
            confidence = stock.realData.trend.confidence;
            recommendation = stock.realData.recommendation.summary;
        } else {
            // Generate mock data
            trendDirection = stock.trend === 'up' ? 'Tăng' : stock.trend === 'down' ? 'Giảm' : 'Trung tính';
            strength = stock.trend === 'up' ?
                ['Rất mạnh', 'Mạnh', 'Khá mạnh'][Math.floor(Math.random() * 3)] :
                stock.trend === 'down' ?
                ['Rất yếu', 'Yếu', 'Khá yếu'][Math.floor(Math.random() * 3)] :
                'Trung bình';

            confidence = stock.trend === 'up' ?
                ['85%', '78%', '72%'][Math.floor(Math.random() * 3)] :
                stock.trend === 'down' ?
                ['68%', '62%', '55%'][Math.floor(Math.random() * 3)] :
                ['52%', '48%', '45%'][Math.floor(Math.random() * 3)];
        }

        // Enhanced recommendations
        const recommendations = {
            up: [
                'Khuyến nghị mua vào khi giá điều chỉnh về vùng hỗ trợ 25,200-25,500.',
                'Xu hướng tăng mạnh, có thể tích lũy thêm ở vùng 24,800-25,100.',
                'Breakout khỏi vùng kháng cự, mục tiêu ngắn hạn 26,500-27,000.',
                'Tín hiệu mua mạnh từ các chỉ báo kỹ thuật, SL đặt dưới 24,500.'
            ],
            down: [
                'Cần thận trọng, chờ tín hiệu đảo chiều tại vùng 24,000-24,300.',
                'Xu hướng giảm, nên chờ xác nhận đáy tại 23,500-23,800.',
                'Áp lực bán mạnh, tránh mua vào cho đến khi có tín hiệu phục hồi.',
                'Có thể short với SL trên 25,800, TP tại 23,200-23,500.'
            ],
            neutral: [
                'Thị trường sideway, theo dõi breakout khỏi vùng 24,500-25,500.',
                'Chờ tín hiệu rõ ràng hơn, hiện tại nên đứng ngoài quan sát.',
                'Tích lũy nhẹ ở vùng hỗ trợ, chờ xác nhận xu hướng mới.',
                'Dao động trong kênh, có thể trade ngắn hạn với R:R 1:2.'
            ]
        };

        // Use real recommendation if available, otherwise use mock
        const finalRecommendation = recommendation || (() => {
            const trendKey = stock.trend === 'up' ? 'up' : stock.trend === 'down' ? 'down' : 'neutral';
            return recommendations[trendKey][Math.floor(Math.random() * recommendations[trendKey].length)];
        })();

        // Generate random but consistent data for MA and Technical indicators
        const maTotal = Math.floor(Math.random() * 3) + 6;
        const maBuy = Math.floor(Math.random() * (maTotal - 2)) + 2;
        const maSell = Math.floor(Math.random() * (maTotal - maBuy)) + 1;
        const maNeutral = maTotal - maBuy - maSell;

        const techTotal = Math.floor(Math.random() * 4) + 8;
        const techBuy = Math.floor(Math.random() * (techTotal - 3)) + 2;
        const techSell = Math.floor(Math.random() * (techTotal - techBuy - 1)) + 1;
        const techNeutral = techTotal - techBuy - techSell;

        return m('tr.border-b.border-border.transition-all.duration-200.hover\\:bg-muted\\/50.hover\\:shadow-sm.cursor-pointer.stock-row.group', {
            'data-symbol': stock.symbol,
            onclick: () => handleStockRowClick(stock.symbol, state)
        }, [
            // Logo column
            m('td.p-4.align-middle.text-center', [
                m('div.w-8.h-8.flex.items-center.justify-center', [
                    m('img', {
                        src: `./assets/${stock.symbol}.svg`,
                        alt: stock.symbol,
                        class: 'w-6 h-6',
                        onerror: function() {
                            this.style.display = 'none';
                            this.nextElementSibling.style.display = 'block';
                        }
                    }),
                    m('span.text-xs.font-bold.text-muted-foreground.hidden', stock.symbol.charAt(0))
                ])
            ]),

            // Symbol column
            m('td.p-4.align-middle', [
                m('span.font-bold.text-base.text-foreground', stock.symbol)
            ]),

            // Price column
            m('td.p-4.align-middle.text-right', [
                m('div.flex.flex-col.items-end', [
                    m('span.font-bold.text-lg.text-foreground', stock.price),
                    m(`span.text-xs.${trendClass}.font-medium`, `${stock.change} (${stock.changePercent})`)
                ])
            ]),

            // Trend column
            m('td.p-4.align-middle', [
                m('div.flex.items-center.gap-3', [
                    m(`div.w-8.h-8.rounded-full.${trendClass === 'text-green-600' ? 'bg-green-100' : trendClass === 'text-red-600' ? 'bg-red-100' : 'bg-gray-100'}.flex.items-center.justify-center`, [
                        m(`span.material-symbols-outlined.text-sm.${trendClass}`, trendIcon)
                    ]),
                    m('div.flex.flex-col', [
                        m(`span.font-medium.text-sm.${trendClass}`, trendDirection),
                        m('div.flex.items-center.gap-2', [
                            m('span.text-xs.text-muted-foreground', strength),
                            m('span.text-xs.px-2.py-0\\.5.rounded-full.bg-yellow-100.text-yellow-700.font-medium', confidence)
                        ])
                    ])
                ])
            ]),

            // Technical analysis column
            m('td.p-4.align-middle.text-center', [
                m('div.flex.flex-col.items-center.gap-3', [
                    // MA Signals
                    m('div.flex.flex-col.items-center.gap-1', [
                        m('div.flex.items-center.gap-1', [
                            m('span.w-6.h-6.flex.items-center.justify-center.rounded-full.bg-gradient-to-br.from-blue-100.to-blue-200.text-blue-800.font-bold.text-xs.border.border-blue-300', maTotal),
                            m('div.flex.rounded-md.overflow-hidden.border.border-gray-200', [
                                m('span.px-1\\.5.py-0\\.5.bg-green-100.text-green-800.text-xs.font-medium', maBuy),
                                m('span.px-1\\.5.py-0\\.5.bg-red-100.text-red-800.text-xs.font-medium', maSell),
                                m('span.px-1\\.5.py-0\\.5.bg-gray-100.text-gray-800.text-xs.font-medium', maNeutral)
                            ])
                        ]),
                        m('span.text-xs.text-muted-foreground', 'MA Signals')
                    ]),

                    // Technical Indicators
                    m('div.flex.flex-col.items-center.gap-1', [
                        m('div.flex.items-center.gap-1', [
                            m('span.w-6.h-6.flex.items-center.justify-center.rounded-full.bg-gradient-to-br.from-purple-100.to-purple-200.text-purple-800.font-bold.text-xs.border.border-purple-300', techTotal),
                            m('div.flex.rounded-md.overflow-hidden.border.border-gray-200', [
                                m('span.px-1\\.5.py-0\\.5.bg-green-100.text-green-800.text-xs.font-medium', techBuy),
                                m('span.px-1\\.5.py-0\\.5.bg-red-100.text-red-800.text-xs.font-medium', techSell),
                                m('span.px-1\\.5.py-0\\.5.bg-gray-100.text-gray-800.text-xs.font-medium', techNeutral)
                            ])
                        ]),
                        m('span.text-xs.text-muted-foreground', 'Technical')
                    ])
                ])
            ]),

            // Recommendation column
            m('td.p-4.align-middle', {
                style: 'min-width: 280px; max-width: 320px;'
            }, [
                m('div.bg-muted\\/30.rounded-lg.p-3.border.border-border', [
                    m('div.flex.items-start.gap-2', [
                        m('span.material-symbols-outlined.text-sm.text-primary.mt-0\\.5', 'lightbulb'),
                        m('div.flex-1', [
                            m('p.text-sm.text-foreground.leading-relaxed', finalRecommendation),
                            m('div.flex.items-center.gap-2.mt-2', [
                                m(`span.px-2.py-1.rounded-full.text-xs.font-medium.${trendClass === 'text-green-600' ? 'bg-green-100 text-green-700' : trendClass === 'text-red-600' ? 'bg-red-100 text-red-700' : 'bg-gray-100 text-gray-700'}`,
                                    stock.trend === 'up' ? 'Tích cực' : stock.trend === 'down' ? 'Thận trọng' : 'Quan sát'
                                ),
                                m('span.text-xs.text-muted-foreground', '• Cập nhật 2h trước')
                            ])
                        ])
                    ])
                ])
            ])
        ]);
    }
};

// Helper function to handle stock row clicks
function handleStockRowClick(symbol, state) {
    // Add new tab for the stock
    state.addTab(symbol);

    // Update localStorage with selected symbols
    const currentSymbols = JSON.parse(localStorage.getItem('selectedSymbols') || '[]');
    if (!currentSymbols.includes(symbol)) {
        currentSymbols.push(symbol);
        localStorage.setItem('selectedSymbols', JSON.stringify(currentSymbols));
    }
}
