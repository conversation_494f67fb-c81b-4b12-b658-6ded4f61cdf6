// Performance Dashboard Component
// Displays application performance metrics and optimization insights

import { performanceMonitor } from '../utils/PerformanceMonitor.js';

export const PerformanceDashboard = {
    oninit: (vnode) => {
        vnode.state.isMonitoring = false;
        vnode.state.report = null;
        vnode.state.autoRefresh = false;
        vnode.state.refreshInterval = null;
    },
    
    onremove: (vnode) => {
        if (vnode.state.refreshInterval) {
            clearInterval(vnode.state.refreshInterval);
        }
        if (vnode.state.isMonitoring) {
            performanceMonitor.stop();
        }
    },
    
    view: (vnode) => {
        return m('div.performance-dashboard.p-6', [
            // Header
            m('div.mb-6', [
                m('h1.text-2xl.font-bold.mb-2', 'Performance Dashboard'),
                m('p.text-muted-foreground', 'Monitor application performance and optimization metrics')
            ]),
            
            // Controls
            m('div.bg-card.border.border-border.rounded-lg.p-6.mb-6', [
                m('h2.text-lg.font-semibold.mb-4', 'Monitoring Controls'),
                
                m('div.flex.items-center.gap-4.mb-4', [
                    // Start/Stop Monitoring
                    m('button.px-4.py-2.rounded-md.transition-colors', {
                        class: vnode.state.isMonitoring ? 
                            'bg-red-600 text-white hover:bg-red-700' : 
                            'bg-green-600 text-white hover:bg-green-700',
                        onclick: () => toggleMonitoring(vnode)
                    }, [
                        m('span.material-symbols-outlined.mr-2', 
                            vnode.state.isMonitoring ? 'stop' : 'play_arrow'),
                        vnode.state.isMonitoring ? 'Stop Monitoring' : 'Start Monitoring'
                    ]),
                    
                    // Generate Report
                    m('button.bg-primary.text-primary-foreground.px-4.py-2.rounded-md.hover\\:bg-primary\\/90.transition-colors', {
                        onclick: () => generateReport(vnode)
                    }, [
                        m('span.material-symbols-outlined.mr-2', 'assessment'),
                        'Generate Report'
                    ]),
                    
                    // Auto Refresh Toggle
                    m('label.flex.items-center.gap-2.cursor-pointer', [
                        m('input[type="checkbox"]', {
                            checked: vnode.state.autoRefresh,
                            onchange: (e) => toggleAutoRefresh(vnode, e.target.checked)
                        }),
                        m('span.text-sm', 'Auto Refresh (5s)')
                    ])
                ]),
                
                // Status
                m('div.text-sm.text-muted-foreground', [
                    vnode.state.isMonitoring ? 
                        m('span.text-green-600', '● Monitoring active') :
                        m('span.text-gray-600', '● Monitoring stopped')
                ])
            ]),
            
            // Performance Report
            vnode.state.report && m(PerformanceReport, { report: vnode.state.report })
        ]);
    }
};

// Performance Report Component
const PerformanceReport = {
    view: (vnode) => {
        const { report } = vnode.attrs;
        
        return m('div.bg-card.border.border-border.rounded-lg.p-6', [
            m('h2.text-lg.font-semibold.mb-4', 'Performance Report'),
            
            // Summary Cards
            m('div.grid.grid-cols-2.md\\:grid-cols-4.gap-4.mb-6', [
                m(MetricCard, {
                    title: 'Monitoring Duration',
                    value: formatDuration(report.summary.monitoringDuration),
                    icon: 'schedule',
                    color: 'blue'
                }),
                m(MetricCard, {
                    title: 'Data Loads',
                    value: report.summary.totalDataLoads.toString(),
                    icon: 'download',
                    color: 'green'
                }),
                m(MetricCard, {
                    title: 'Chart Renders',
                    value: report.summary.totalChartRenders.toString(),
                    icon: 'show_chart',
                    color: 'purple'
                }),
                m(MetricCard, {
                    title: 'Component Updates',
                    value: report.summary.totalComponentUpdates.toString(),
                    icon: 'refresh',
                    color: 'orange'
                })
            ]),
            
            // Detailed Metrics
            m('div.grid.grid-cols-1.lg\\:grid-cols-2.gap-6.mb-6', [
                // Data Loading Performance
                m('div', [
                    m('h3.text-md.font-semibold.mb-3', 'Data Loading Performance'),
                    m('div.space-y-2', [
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Average Load Time:'),
                            m('span.font-medium', `${report.dataLoading.average.toFixed(0)}ms`)
                        ]),
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Slowest Load:'),
                            m('span.font-medium', `${report.dataLoading.slowest.toFixed(0)}ms`)
                        ]),
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Success Rate:'),
                            m('span.font-medium.text-green-600', `${report.dataLoading.successRate}%`)
                        ]),
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Failures:'),
                            m('span.font-medium.text-red-600', report.dataLoading.failures.toString())
                        ])
                    ])
                ]),
                
                // Chart Rendering Performance
                m('div', [
                    m('h3.text-md.font-semibold.mb-3', 'Chart Rendering Performance'),
                    m('div.space-y-2', [
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Average Render Time:'),
                            m('span.font-medium', `${report.chartRendering.average.toFixed(0)}ms`)
                        ]),
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Slowest Render:'),
                            m('span.font-medium', `${report.chartRendering.slowest.toFixed(0)}ms`)
                        ]),
                        m('div.flex.justify-between', [
                            m('span.text-muted-foreground', 'Average FPS:'),
                            m('span.font-medium', `${report.chartRendering.averageFPS.toFixed(1)}`)
                        ])
                    ])
                ])
            ]),
            
            // Memory Usage
            m('div.mb-6', [
                m('h3.text-md.font-semibold.mb-3', 'Memory Usage'),
                m('div.grid.grid-cols-1.md\\:grid-cols-3.gap-4', [
                    m('div.bg-muted\\/30.rounded-lg.p-4', [
                        m('div.text-sm.text-muted-foreground.mb-1', 'Current Usage'),
                        m('div.text-xl.font-bold', formatBytes(report.memoryUsage.current)),
                        m('div.text-xs.text-muted-foreground', `${report.memoryUsage.utilizationPercent}% of limit`)
                    ]),
                    m('div.bg-muted\\/30.rounded-lg.p-4', [
                        m('div.text-sm.text-muted-foreground.mb-1', 'Peak Usage'),
                        m('div.text-xl.font-bold', formatBytes(report.memoryUsage.peak))
                    ]),
                    m('div.bg-muted\\/30.rounded-lg.p-4', [
                        m('div.text-sm.text-muted-foreground.mb-1', 'Trend'),
                        m('div.text-xl.font-bold', {
                            class: report.memoryUsage.trend === 'increasing' ? 'text-red-600' : 'text-green-600'
                        }, report.memoryUsage.trend)
                    ])
                ])
            ]),
            
            // Network Performance
            m('div.mb-6', [
                m('h3.text-md.font-semibold.mb-3', 'Network Performance'),
                m('div.grid.grid-cols-1.md\\:grid-cols-2.gap-4', [
                    m('div.bg-muted\\/30.rounded-lg.p-4', [
                        m('div.text-sm.text-muted-foreground.mb-1', 'Total Requests'),
                        m('div.text-xl.font-bold', report.networkRequests.total.toString())
                    ]),
                    m('div.bg-muted\\/30.rounded-lg.p-4', [
                        m('div.text-sm.text-muted-foreground.mb-1', 'Cache Hit Rate'),
                        m('div.text-xl.font-bold.text-green-600', `${report.networkRequests.cacheHitRate}%`)
                    ])
                ])
            ]),
            
            // Recommendations
            report.recommendations.length > 0 && m('div', [
                m('h3.text-md.font-semibold.mb-3', 'Optimization Recommendations'),
                m('div.space-y-2', 
                    report.recommendations.map(rec =>
                        m('div.flex.items-start.gap-2.p-3.bg-yellow-50.border.border-yellow-200.rounded-md', [
                            m('span.material-symbols-outlined.text-yellow-600.mt-0.5', 'lightbulb'),
                            m('span.text-sm.text-yellow-800', rec)
                        ])
                    )
                )
            ])
        ]);
    }
};

// Metric Card Component
const MetricCard = {
    view: (vnode) => {
        const { title, value, icon, color } = vnode.attrs;
        
        const colorClasses = {
            blue: 'text-blue-600',
            green: 'text-green-600',
            purple: 'text-purple-600',
            orange: 'text-orange-600',
            red: 'text-red-600'
        };
        
        return m('div.bg-muted\\/30.rounded-lg.p-4', [
            m('div.flex.items-center.gap-2.mb-2', [
                m(`span.material-symbols-outlined.${colorClasses[color]}`, icon),
                m('div.text-sm.text-muted-foreground', title)
            ]),
            m('div.text-xl.font-bold', value)
        ]);
    }
};

// Helper Functions
function toggleMonitoring(vnode) {
    if (vnode.state.isMonitoring) {
        const report = performanceMonitor.stop();
        vnode.state.report = report;
        vnode.state.isMonitoring = false;
    } else {
        performanceMonitor.start();
        vnode.state.isMonitoring = true;
    }
    m.redraw();
}

function generateReport(vnode) {
    if (vnode.state.isMonitoring) {
        // Generate intermediate report without stopping monitoring
        vnode.state.report = performanceMonitor.generateReport();
    } else {
        // Show last report or empty state
        if (!vnode.state.report) {
            vnode.state.report = {
                summary: { monitoringDuration: 0, totalDataLoads: 0, totalChartRenders: 0, totalComponentUpdates: 0 },
                dataLoading: { average: 0, slowest: 0, successRate: '100', failures: 0 },
                chartRendering: { average: 0, slowest: 0, averageFPS: 0 },
                memoryUsage: { current: 0, peak: 0, trend: 'stable', utilizationPercent: '0' },
                networkRequests: { total: 0, cacheHitRate: '0' },
                recommendations: ['Start monitoring to see performance recommendations']
            };
        }
    }
    m.redraw();
}

function toggleAutoRefresh(vnode, enabled) {
    vnode.state.autoRefresh = enabled;
    
    if (enabled) {
        vnode.state.refreshInterval = setInterval(() => {
            if (vnode.state.isMonitoring) {
                generateReport(vnode);
            }
        }, 5000);
    } else {
        if (vnode.state.refreshInterval) {
            clearInterval(vnode.state.refreshInterval);
            vnode.state.refreshInterval = null;
        }
    }
    
    m.redraw();
}

function formatDuration(ms) {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    if (ms < 3600000) return `${(ms / 60000).toFixed(1)}m`;
    return `${(ms / 3600000).toFixed(1)}h`;
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
}
